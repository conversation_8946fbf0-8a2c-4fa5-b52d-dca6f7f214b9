{# Email Scheduling Manager #}

<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap4.min.css">

<!-- Page Title -->
<h1 class="mb-4">
  <i class="bi bi-clock text-info"></i> Email Scheduling Manager
</h1>

<!-- Sub-Navigation -->
<ul class="nav nav-tabs mb-4">
  <li class="nav-item">
    <a class="nav-link active" href="/admin/scheduling">
      <i class="bi bi-calendar-check"></i> Scheduled
    </a>
  </li>
  <li class="nav-item">
    <a class="nav-link" href="/admin/preview?tpl=/Admin/Campaign/sent">
      <i class="bi bi-send"></i> Sent
    </a>
  </li>
</ul>

<!-- Quick Actions -->
<div class="row mb-4">
  <div class="col-md-6">
    <div class="card">
      <div class="card-body">
        <h5 class="card-title">
          <i class="bi bi-lightning text-warning"></i> Schedule Instant Email
        </h5>
        <p class="card-text">Schedule an instant email for a specific article to be sent at a future time.</p>
        <a href="/admin/news/post" class="btn btn-warning">
          <i class="bi bi-plus-circle"></i> Create Scheduled Article
        </a>
      </div>
    </div>
  </div>
  <div class="col-md-6">
    <div class="card">
      <div class="card-body">
        <h5 class="card-title">
          <i class="bi bi-arrow-repeat text-primary"></i> Manage Templates
        </h5>
        <p class="card-text">Configure daily, weekly, and recurring email templates and schedules.</p>
        <a href="/admin/email-temp" class="btn btn-primary">
          <i class="bi bi-gear"></i> Email Templates
        </a>
      </div>
    </div>
  </div>
</div>

<!-- Filters Form (collapsed by default) -->
<div class="collapse" id="filterForm">
  <form class="card card-body mb-5 bg-light">
    <div class="form-row">
      <div class="form-group col-md-6">
        <label for="typeFilter" class="font-weight-bold">Type</label>
        <select id="typeFilter" class="form-control">
          <option value="">All</option>
          <option>Instant</option>
          <option>Daily</option>
          <option>Weekly</option>
          <option>Invoice</option>
          <option>Workflow</option>
          <option>Report</option>
        </select>
      </div>
      <div class="form-group col-md-6">
        <label for="statusFilter" class="font-weight-bold">Status</label>
        <select id="statusFilter" class="form-control">
          <option value="">All</option>
          <option>Sending</option>
          <option>Scheduled</option>
          <option>Paused</option>
          <option>Draft</option>
          <option>Unpublished</option>
        </select>
      </div>
    </div>
    <div class="d-flex justify-content-end">
      <button type="button" id="applyFilter" class="btn btn-primary">Apply Filters</button>
      <button type="button" id="resetFilter" class="btn btn-secondary ml-2">Reset</button>
    </div>
  </form>
</div>

<!-- Filter Toggle Button -->
<div class="d-flex justify-content-between align-items-center mb-3">
  <div>
    <button class="btn btn-outline-secondary" type="button" data-toggle="collapse" data-target="#filterForm">
      <i class="bi bi-funnel"></i> Filters
    </button>
  </div>
  <div>
    <button class="btn btn-outline-success mr-2" id="processQueueBtn">
      <i class="bi bi-send"></i> Process Email Queue
    </button>
    <button class="btn btn-outline-primary" onclick="location.reload()">
      <i class="bi bi-arrow-clockwise"></i> Refresh
    </button>
  </div>
</div>

<!-- Main Table -->
<table id="schedulingTable" class="table table-bordered table-striped my-2">
  <thead>
    <tr>
      <th class="text-center"></th>
      <th class="d-none">Type</th>
      <th>Campaign</th>
      <th>Subject</th>
      <th class="text-nowrap">Send Time</th>
      <th>Status</th>
      <th class="text-nowrap">Actions</th>
    </tr>
  </thead>
</table>

<!-- Loading State -->
<div id="loadingState" class="text-center py-5" style="display: none;">
  <div class="spinner-border text-primary" role="status">
    <span class="sr-only">Loading...</span>
  </div>
  <p class="mt-2">Loading scheduled emails...</p>
</div>

<!-- Empty State -->
<div id="emptyState" class="text-center py-5" style="display: none;">
  <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
  <h4 class="text-muted mt-3">No Scheduled Emails</h4>
  <p class="text-muted">Get started by scheduling your first email campaign.</p>
  <a href="/admin/news/post" class="btn btn-primary">
    <i class="bi bi-plus-circle"></i> Schedule Email
  </a>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document" style="max-width:90%;">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title mb-0" id="previewModalLabel">Email Preview</h5>
        <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
      </div>
      <div class="modal-body p-0">
        <iframe
          id="previewFrame"
          style="width:100%; height:600px; border:none;"
          sandbox="allow-same-origin allow-scripts allow-popups"
          src="">
        </iframe>
      </div>
      <div class="modal-footer">
        <div class="input-group w-50">
          <input id="previewEmailAddress" type="email" class="form-control" placeholder="Enter email address">
          <div class="input-group-append">
            <button class="btn btn-primary send-preview-btn" data-toggle="tooltip" title="Send Preview">
              <i class="bi bi-send"></i>
            </button>
          </div>
        </div>
        <button class="btn btn-secondary ml-3" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- DataTables JavaScript -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap4.min.js"></script>

<script>
$(function(){
  // Initialize DataTable with real database data
  var table = $('#schedulingTable').DataTable({
    ajax: {
      url: '/admin/scheduling/api',
      dataSrc: function(json) {
        console.log('API Response:', json);
        console.log('Number of campaigns:', json.length);
        return json;
      },
      error: function(xhr, error, thrown) {
        console.error('Failed to load campaigns:', error);
        $('#emptyState').show().find('h4').text('Failed to Load Campaigns');
        $('#emptyState').find('p').text('Please refresh the page to try again.');
      }
    },
    columns: [
      // Icon + tooltip = Type
      { data:'icon', orderable:false, className:'text-center',
        render: function(icon,_,row){
          return '<i class="bi bi-'+icon+'" data-toggle="tooltip" title="'+ row.type +'"></i>';
        }
      },
      // Type (hidden but used for filtering)
      { data:'type', visible:false },
      // Campaign (now visible)
      { data:'campaign', orderable:false },
      // Subject + stats + preview
      { data:null, orderable:false, render:function(r){
          // Generate preview URL using existing camp-preview system
          var previewUrl = '/admin/camp-preview/' + r.id + '/0';
          var q=r.queued.toLocaleString(),
              s=r.sent.toLocaleString(),
              e=r.errors.toLocaleString(),
              p=r.queued>0?Math.round(r.sent/r.queued*100):0;
          return '<a href="#" class="preview-link" data-preview="'+previewUrl+'">'+r.subject+'</a>'
               +'<br><small class="text-muted">'
               +'<strong>Recipients:</strong> '+r.user_group+' ('+r.num_users.toLocaleString()+')'
               +' <strong class="ml-2">Queued:</strong> '+q
               +' <strong class="ml-2">Sent:</strong> '+s+' ('+p+'%)'
               +' <strong class="ml-2">Errors:</strong> '+e
               +'</small>';
        }
      },
      // Send Time (formatted)
      { data:'send_time', className:'text-nowrap', orderable:true,
        render:function(dt,type){
          if(!dt) return '';
          var d=new Date(dt);
          if(isNaN(d)) return '';
          if(type==='sort'||type==='type') return d.getTime();
          var monthNames = ['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'];
          var pad=n=>('0'+n).slice(-2);
          return pad(d.getDate())+'-'+monthNames[d.getMonth()]+'-'+d.getFullYear()
               +' '+pad(d.getHours())+':'+pad(d.getMinutes());
        }
      },
      // Status badge
      { data:'status', orderable:true, render:function(s){
          var m={'Sending':'danger','Scheduled':'success','Paused':'warning',
                 'Draft':'secondary','Unpublished':'secondary'};
          return '<span class="badge badge-'+(m[s]||'primary')+'">'+s+'</span>';
        }
      },
      // Actions
      { data:'actions', orderable:false, className:'text-nowrap',
        render:function(actions,_,row){
          var html='';
          actions.forEach(function(a){
            var icon, attrs=' data-toggle="tooltip" title="'+a+'"';
            switch(a){
              case 'Edit':
                icon='<i class="bi bi-pencil"></i>';
                html+='<button class="btn btn-sm btn-primary edit-btn mr-1"'+attrs
                     +' data-id="'+row.id+'">'+icon+'</button>';
                break;
              case 'Stop': case 'Pause':
                icon='<i class="bi bi-stop-circle"></i>';
                html+='<button class="btn btn-sm btn-warning pause-btn mr-1"'+attrs+'>'+icon+'</button>';
                break;
              case 'Resume':
                icon='<i class="bi bi-arrow-clockwise"></i>';
                html+='<button class="btn btn-sm btn-secondary resume-btn mr-1"'+attrs+'>'+icon+'</button>';
                break;
              case 'Delete':
                icon='<i class="bi bi-trash"></i>';
                html+='<button class="btn btn-sm btn-danger delete-btn mr-1"'+attrs+'>'+icon+'</button>';
                break;
            }
          });
          return html;
        }
      }
    ],
    order:[[4,'asc']],
    paging:true, info:true, searching:true,
    processing: true,
    language: {
      processing: '<div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div>',
      emptyTable: "No scheduled emails found",
      zeroRecords: "No matching campaigns found"
    },
    drawCallback: function() {
      $('[data-toggle="tooltip"]').tooltip();

      var dataLength = this.api().data().length;
      console.log('DataTable draw callback - rows:', dataLength);

      // Show/hide empty state based on data
      if (dataLength === 0) {
        console.log('Showing empty state');
        $('#emptyState').show();
      } else {
        console.log('Hiding empty state, showing', dataLength, 'campaigns');
        $('#emptyState').hide();
      }
    }
  });

  // Preview → iframe
  $('#schedulingTable').on('click','.preview-link',function(e){
    e.preventDefault();
    var r=table.row($(this).closest('tr')).data();
    $('#previewFrame').attr('src',$(this).data('preview'));
    $('#previewEmailAddress').val('');
    $('#previewModal').modal('show');
  });

  // Send preview
  $('.send-preview-btn').click(function(){
    var email=$('#previewEmailAddress').val().trim();
    if(!email) return alert('Enter an email address');
    var $b=$(this).prop('disabled',true).html('<span class="spinner-border spinner-border-sm"></span>');
    setTimeout(function(){
      $b.prop('disabled',false).html('<i class="bi bi-send"></i>');
      alert('Preview sent to '+email);
    },1000);
  });

  // Filters
  $('#applyFilter').click(function(){
    table.column(1).search($('#typeFilter').val()).draw();
    table.column(5).search($('#statusFilter').val()).draw();
  });
  $('#resetFilter').click(function(){
    $('#typeFilter,#statusFilter').val('');
    table.search('').columns().search('').draw();
  });

  // Process Email Queue button
  $('#processQueueBtn').click(function(){
    var $btn = $(this);
    var originalText = $btn.html();

    $btn.prop('disabled', true)
        .html('<span class="spinner-border spinner-border-sm mr-2"></span>Processing...');

    $.ajax({
      url: '/admin/scheduling/processQueue',
      method: 'GET',
      dataType: 'json',
      success: function(response) {
        if (response.success) {
          var message = 'Success: ' + response.message;
          if (response.debug) {
            message += '\n\nDebug Info:';
            message += '\n- Total pending: ' + response.debug.total_pending;
            message += '\n- Total sent: ' + response.debug.total_sent;
          }
          if (response.log && response.log.length > 0) {
            message += '\n\nDetailed Log:\n' + response.log.join('\n');
          }
          alert(message);
          // Refresh the table to show updated counts
          table.ajax.reload();
        } else {
          alert('Error: ' + (response.error || 'Unknown error'));
        }
      },
      error: function(xhr, status, error) {
        alert('Failed to process queue: ' + error);
      },
      complete: function() {
        $btn.prop('disabled', false).html(originalText);
      }
    });
  });

  console.log('Scheduling Manager initialized with real database data - Phase 2 complete!');
});
</script>
