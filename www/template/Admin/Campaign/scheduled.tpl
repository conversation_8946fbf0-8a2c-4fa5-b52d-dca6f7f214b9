{# scheduled.tpl #}

<!-- Page Title -->
<h1 class="mb-4">Scheduled Emails</h1>

<!-- Sub-Navigation -->
{% include '/Admin/Campaign/inc-nav.tpl' %}

<!-- Filters Form (collapsed by default) -->
<div class="collapse" id="filterForm">
  <form class="card card-body mb-5 bg-light">
    <div class="form-row">
      <div class="form-group col-md-6">
        <label for="typeFilter" class="font-weight-bold">Type</label>
        <select id="typeFilter" class="form-control">
          <option value="">All</option>
          <option>Instant</option>
          <option>Daily</option>
          <option>Weekly</option>
          <option>Invoice</option>
          <option>Workflow</option>
          <option>Report</option>
        </select>
      </div>
      <div class="form-group col-md-6">
        <label for="statusFilter" class="font-weight-bold">Status</label>
        <select id="statusFilter" class="form-control">
          <option value="">All</option>
          <option>Sending</option>
          <option>Repeating</option>
          <option>Paused</option>
          <option>Scheduled</option>
          <option>Draft</option>
          <option>Unpublished</option>
        </select>
      </div>
    </div>
    <div class="d-flex justify-content-end">
      <button type="button" id="applyFilter" class="btn btn-primary">Apply Filters</button>
      <button type="button" id="resetFilter" class="btn btn-secondary ml-2">Reset</button>
    </div>
  </form>
</div>

<!-- Main Table -->
<table id="campaignTable" class="table table-bordered table-striped my-2">
  <thead>
    <tr>
      <th class="text-center"></th>
      <th class="d-none">Type</th>
      <th>Campaign</th>
      <th>Subject</th>
      <th class="text-nowrap">Send</th>
      <th>Status</th>
      <th class="text-nowrap">Actions</th>
    </tr>
  </thead>
</table>

<!-- Developer Notes -->
<div id="devNotes" class="card card-body bg-light mt-4">
  <h5 class="mb-3 text-muted">Notes</h5>
  <p>See: "Email Templates" Modal buttons - Update Info / Text</p>
  <ul class="mb-0">
    <li>Instant: /admin/email-temp/1</li>
    <li>Daily: /admin/email-temp/2</li>
    <li>Weekly: /admin/email-temp/3</li>
  </ul>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document" style="max-width:90%;">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title mb-0" id="previewModalLabel">Email Preview</h5>
        <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
      </div>
      <div class="modal-body p-0">
        <iframe
          id="previewFrame"
          style="width:100%; height:600px; border:none;"
          sandbox="allow-same-origin allow-scripts allow-popups"
          src="">
        </iframe>
      </div>
      <div class="modal-footer">
        <div class="input-group w-50">
          <input id="previewEmailAddress" type="email" class="form-control" placeholder="Enter email address">
          <div class="input-group-append">
            <button class="btn btn-primary send-preview-btn" data-toggle="tooltip" title="Send Preview">
              <i class="bi bi-send"></i>
            </button>
          </div>
        </div>
        <button class="btn btn-secondary ml-3" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- Recurring Edit Modal -->
<div class="modal fade" id="recurringModal" tabindex="-1" role="dialog" aria-labelledby="recurringModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <form id="recurringForm" class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title mb-0" id="recurringModalLabel">Edit Recurring Campaign</h5>
        <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="recCampaignName">Campaign Name</label>
          <input type="text" id="recCampaignName" name="campaignName" class="form-control" required>
        </div>
        <div class="form-group">
          <label for="recEmailSubject">Email Subject</label>
          <input type="text" id="recEmailSubject" name="emailSubject" class="form-control" required>
        </div>
        <div class="form-group">
          <label for="recRecipients">Recipients</label>
          <input type="text" id="recRecipients" name="recipients" class="form-control">
        </div>
        <div class="form-group">
          <label>Frequency: Day</label><br>
          {% for d in ['Mon','Tue','Wed','Thu','Fri','Sat','Sun'] %}
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="checkbox" id="recFreq{{ d }}" name="frequencyDays" value="{{ d }}">
              <label class="form-check-label" for="recFreq{{ d }}">{{ d }}</label>
            </div>
          {% endfor %}
        </div>
        <div class="form-group">
          <label for="recFrequencyTime">Time</label>
          <input type="time" id="recFrequencyTime" name="frequencyTime" class="form-control" required>
        </div>
        <input type="hidden" id="recCampaignId" name="campaignId">
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary">Save</button>
        <button type="button" class="btn btn-warning pause-recurring-btn">Pause</button>
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
      </div>
    </form>
  </div>
</div>

<script>
$(function(){
  // Throw on invalid JSON
  $.fn.dataTable.ext.errMode = 'throw';

  var monthNames = ['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'];
  var currentEditId = null, currentPreviewId = null;

  var table = $('#campaignTable').DataTable({
    ajax: { url: '/files/scheduled-emails.json', dataSrc: '' },
    columns: [
      // Icon + tooltip = Type
      { data:'icon', orderable:false, className:'text-center',
        render: function(icon,_,row){
          return '<i class="bi bi-'+icon+'" data-toggle="tooltip" title="'+ row.type +'"></i>';
        }
      },
      // Type (hidden but used for filtering)
      { data:'type', visible:false },
      // Campaign (now visible)
      { data:'campaign', orderable:false },
      // Subject + stats + preview
      { data:null, orderable:false, render:function(r){
          var t=r.type.toLowerCase();
          if(t==='weekly') t='daily';
          var url='/preview?tpl=oa/view/Email/'+t,
              q=r.queued.toLocaleString(),
              s=r.sent.toLocaleString(),
              e=r.errors.toLocaleString(),
              p=r.queued>0?Math.round(r.sent/r.queued*100):0;
          return '<a href="#" class="preview-link" data-preview="'+url+'">'+r.subject+'</a>'
               +'<br><small class="text-muted">'
               +'<strong>Recipients:</strong> '+r.user_group+' ('+r.num_users.toLocaleString()+')'
               +' <strong class="ml-2">Queued:</strong> '+q
               +' <strong class="ml-2">Sent:</strong> '+s+' ('+p+'%)'
               +' <strong class="ml-2">Errors:</strong> '+e
               +'</small>';
        }
      },
      // Send (sortable + formatted)
      { data:'send_time', className:'text-nowrap', orderable:true,
        render:function(dt,type){
          if(!dt) return '';
          var d=new Date(dt);
          if(isNaN(d)) return '';
          if(type==='sort'||type==='type') return d.getTime();
          var pad=n=>('0'+n).slice(-2);
          return pad(d.getDate())+'-'+monthNames[d.getMonth()]+'-'+d.getFullYear()
               +' '+pad(d.getHours())+':'+pad(d.getMinutes());
        }
      },
      // Status badge
      { data:'status', orderable:true, render:function(s){
          var m={'Sending':'danger','Repeating':'info','Paused':'warning',
                 'Scheduled':'success','Draft':'secondary','Unpublished':'secondary'};
          return '<span class="badge badge-'+(m[s]||'primary')+'">'+s+'</span>';
        }
      },
      // Actions
      { data:'actions', orderable:false, className:'text-nowrap',
        render:function(actions,_,row){
          var html='';
          actions.forEach(function(a){
            var icon, attrs=' data-toggle="tooltip" title="'+a+'"';
            switch(a){
              case 'Edit':
                if(row.type==='Instant'){
                  icon='<i class="bi bi-pencil"></i>';
                  html+='<button class="btn btn-sm btn-primary edit-btn mr-1"'+attrs
                       +' data-id="'+row.id+'">'+icon+'</button>';
                } else {
                  icon='<i class="bi bi-arrow-repeat"></i>';
                  html+='<button class="btn btn-sm btn-primary edit-recurring-btn mr-1"'+attrs
                       +' data-id="'+row.id+'">'+icon+'</button>';
                }
                break;
              case 'Pause':
                icon='<i class="bi bi-pause-circle"></i>';
                html+='<button class="btn btn-sm btn-warning pause-btn mr-1"'+attrs+' data-id="'+row.id+'">'+icon+'</button>';
                break;
              case 'Resume':
                icon='<i class="bi bi-arrow-clockwise"></i>';
                html+='<button class="btn btn-sm btn-secondary resume-btn mr-1"'+attrs+' data-id="'+row.id+'">'+icon+'</button>';
                break;
              case 'Delete':
                icon='<i class="bi bi-trash"></i>';
                html+='<button class="btn btn-sm btn-danger delete-btn mr-1"'+attrs+'>'+icon+'</button>';
                break;
              case 'Preview':
                icon='<i class="bi bi-send-check"></i>';
                html+='<button class="btn btn-sm btn-success btn-preview mr-1"'+attrs+'>'+icon+'</button>';
                break;
            }
          });
          return html;
        }
      }
    ],
    order:[[4,'asc']],
    paging:true, info:true, searching:true,
    drawCallback:()=>$('[data-toggle="tooltip"]').tooltip()
  });

  // Preview → iframe
  $('#campaignTable').on('click','.preview-link',function(e){
    e.preventDefault();
    var r=table.row($(this).closest('tr')).data();
    currentPreviewId=r.id;
    $('#previewFrame').attr('src',$(this).data('preview'));
    $('#previewEmailAddress').val('');
    $('#previewModal').modal('show');
  });

  // Send preview
  $('.send-preview-btn').click(function(){
    var email=$('#previewEmailAddress').val().trim();
    if(!email) return alert('Enter an email address');
    var $b=$(this).prop('disabled',true).html('<span class="spinner-border spinner-border-sm"></span>');
    setTimeout(function(){
      $b.prop('disabled',false).html('<i class="bi bi-send"></i>');
      alert('Preview sent to '+email);
    },1000);
  });

  // Instant edit redirect
  $('#campaignTable').on('click','.edit-btn',function(){
    window.location.href='/admin/news/post?campaign_id='+$(this).data('id');
  });

  // Recurring edit modal
  $('#campaignTable').on('click','.edit-recurring-btn',function(){
    var r=table.row($(this).closest('tr')).data();
    currentEditId=r.id;
    $('#recCampaignId').val(r.id);
    $('#recCampaignName').val(r.campaign);
    $('#recEmailSubject').val(r.subject);
    $('#recRecipients').val(r.user_group);
    $('#recurringForm input[name="frequencyDays"]').prop('checked',false);
    var d=new Date(r.send_time),pad=n=>('0'+n).slice(-2);
    $('#recFrequencyTime').val(pad(d.getHours())+':'+pad(d.getMinutes()));
    $('#recurringModal').modal('show');
  });

  // Save recurring
  $('#recurringForm').submit(function(e){
    e.preventDefault();
    console.log('Save recurring',currentEditId,$(this).serialize());
    $('#recurringModal').modal('hide');
  });

  // Pause recurring
  $('.pause-recurring-btn').click(function(){
    console.log('Pause recurring',currentEditId);
    $('#recurringModal').modal('hide');
  });

  // In-table pause/resume/stop toggle + hide tooltip
  $('#campaignTable').on('click','.pause-btn',function(){
    $(this).tooltip('hide');
    var $btn = $(this);
    var row = table.row($btn.closest('tr'));
    var data = row.data();
    var campaignId = $btn.data('id');

    $btn.prop('disabled', true);

    $.ajax({
      url: '/admin/scheduling/pause/' + campaignId,
      method: 'POST',
      dataType: 'json',
      success: function(response) {
        if (response.success) {
          data.status = 'Paused';
          data.actions = ['Resume', 'Delete'];
          row.data(data).draw(false);
        }
      },
      complete: function() {
        $btn.prop('disabled', false);
      }
    });
  });

  $('#campaignTable').on('click','.resume-btn',function(){
    $(this).tooltip('hide');
    var $btn = $(this);
    var row = table.row($btn.closest('tr'));
    var data = row.data();
    var campaignId = $btn.data('id');

    $btn.prop('disabled', true);

    $.ajax({
      url: '/admin/scheduling/resume/' + campaignId,
      method: 'POST',
      dataType: 'json',
      success: function(response) {
        if (response.success) {
          data.status = 'Sending';
          data.actions = ['Pause'];
          row.data(data).draw(false);
        }
      },
      complete: function() {
        $btn.prop('disabled', false);
      }
    });
  });

  // Filters
  $('#applyFilter').click(function(){
    table.column(1).search($('#typeFilter').val()).draw();
    table.column(5).search($('#statusFilter').val()).draw();
  });
  $('#resetFilter').click(function(){
    $('#typeFilter,#statusFilter').val('');
    table.search('').columns().search('').draw();
  });
});
</script>
