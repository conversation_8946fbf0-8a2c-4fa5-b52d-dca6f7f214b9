<?php
namespace App\News\Admin;

class SchedulingController extends Base
{
    public function home() {
        $this->set('page.title', 'Email Scheduling Manager');
        $this->view = '/Admin/Scheduling/home.tpl';
    }

    /**
     * API endpoint to get scheduled campaigns data
     */
    public function api() {
        header('Content-Type: application/json');

        try {
            $campaigns = $this->getScheduledCampaigns();

            // Add debug info if requested
            if (isset($_GET['debug'])) {
                $debug = [
                    'campaigns' => $campaigns,
                    'total_campaigns' => $this->db->from('blog_email_camp')->count(),
                    'instant_campaigns' => $this->db->from('blog_email_camp c')
                        ->join('blog_email_temp t', 'c.temp_id = t.id')
                        ->where('t.temp_type', 'Instant')
                        ->count(),
                    'recent_campaigns' => $this->db->from('blog_email_camp')
                        ->where('queued_ts >=', date('Y-m-d H:i:s', strtotime('-1 day')))
                        ->count()
                ];
                echo json_encode($debug);
            } else {
                echo json_encode($campaigns);
            }
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to fetch campaigns: ' . $e->getMessage()]);
        }
        exit;
    }

    /**
     * Process email queue manually (for testing/development)
     */
    public function processQueue() {
        header('Content-Type: application/json');

        try {
            $publishService = new \App\News\Email\PublishService();
            $processed = $publishService->processEmailQueue(20);
            $log = $publishService->getLog();

            echo json_encode([
                'success' => true,
                'processed' => $processed,
                'message' => "Processed $processed emails",
                'log' => $log,
                'debug' => [
                    'total_pending' => $this->db->from('blog_email')->where('sent', 0)->where('err', 0)->count(),
                    'total_sent' => $this->db->from('blog_email')->where('sent', 1)->count()
                ]
            ]);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to process queue: ' . $e->getMessage()
            ]);
        }
        exit;
    }

    /**
     * Test endpoint to verify routing
     */
    public function test() {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'Test endpoint working',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        exit;
    }

    /**
     * Pause a campaign - sets err = -1 for unsent emails
     */
    public function pause($campaignId) {
        header('Content-Type: application/json');

        // Log that the method was called
        error_log("SchedulingController::pause() called with campaignId: " . $campaignId);

        try {
            $campaignId = (int)$campaignId;
            if ($campaignId <= 0) {
                throw new \Exception('Invalid campaign ID: ' . $campaignId);
            }

            // Verify campaign exists
            $campaign = $this->db->from('blog_email_camp')->where('id', $campaignId)->row();
            if (!$campaign) {
                throw new \Exception('Campaign not found with ID: ' . $campaignId);
            }

            // Check if there are active emails to pause
            $activeCount = $this->db->from('blog_email')
                                   ->where('camp_id', $campaignId)
                                   ->where('sent', 0)
                                   ->where('err', 0)
                                   ->count();

            if ($activeCount == 0) {
                throw new \Exception('No active emails found for campaign ' . $campaignId);
            }

            // Pause unsent emails (set err = -1)
            $affected = $this->db->where('camp_id', $campaignId)
                                 ->where('sent', 0)
                                 ->where('err', 0)
                                 ->update('blog_email', ['err' => -1]);

            echo json_encode([
                'success' => true,
                'message' => "Campaign paused successfully",
                'affected_emails' => $affected,
                'debug' => [
                    'campaign_id' => $campaignId,
                    'active_count' => $activeCount,
                    'affected_rows' => $affected
                ]
            ]);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to pause campaign: ' . $e->getMessage(),
                'debug' => [
                    'campaign_id' => $campaignId ?? 'unknown',
                    'line' => $e->getLine(),
                    'file' => basename($e->getFile())
                ]
            ]);
        }
        exit;
    }

    /**
     * Resume a campaign - sets err = 0 for paused emails
     */
    public function resume($campaignId) {
        header('Content-Type: application/json');

        try {
            $campaignId = (int)$campaignId;
            if ($campaignId <= 0) {
                throw new \Exception('Invalid campaign ID: ' . $campaignId);
            }

            // Verify campaign exists
            $campaign = $this->db->from('blog_email_camp')->where('id', $campaignId)->row();
            if (!$campaign) {
                throw new \Exception('Campaign not found with ID: ' . $campaignId);
            }

            // Check if there are paused emails to resume
            $pausedCount = $this->db->from('blog_email')
                                   ->where('camp_id', $campaignId)
                                   ->where('sent', 0)
                                   ->where('err', -1)
                                   ->count();

            if ($pausedCount == 0) {
                throw new \Exception('No paused emails found for campaign ' . $campaignId);
            }

            // Resume paused emails (set err = 0)
            $affected = $this->db->where('camp_id', $campaignId)
                                 ->where('sent', 0)
                                 ->where('err', -1)
                                 ->update('blog_email', ['err' => 0]);

            echo json_encode([
                'success' => true,
                'message' => "Campaign resumed successfully",
                'affected_emails' => $affected,
                'debug' => [
                    'campaign_id' => $campaignId,
                    'paused_count' => $pausedCount,
                    'affected_rows' => $affected
                ]
            ]);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to resume campaign: ' . $e->getMessage(),
                'debug' => [
                    'campaign_id' => $campaignId ?? 'unknown',
                    'line' => $e->getLine(),
                    'file' => basename($e->getFile())
                ]
            ]);
        }
        exit;
    }



    /**
     * Get scheduled campaigns from database
     */
    private function getScheduledCampaigns() {
        // Get campaigns with their email statistics
        $query = "
            SELECT
                c.id,
                c.temp_id,
                c.subject,
                c.txt,
                c.queued_ts,
                t.name as template_name,
                t.temp_type,
                t.users as user_group,
                t.repeat_at,

                -- Email statistics
                COUNT(e.id) as num_users,
                COUNT(CASE WHEN e.sent = 0 AND e.err >= 0 THEN 1 END) as queued,
                COUNT(CASE WHEN e.sent > 0 THEN 1 END) as sent,
                COUNT(CASE WHEN e.err > 0 THEN 1 END) as errors,
                COUNT(CASE WHEN e.opened > 0 THEN 1 END) as opened,
                COUNT(CASE WHEN e.clicked > 0 THEN 1 END) as clicked,
                COUNT(CASE WHEN e.unsub > 0 THEN 1 END) as unsubscribed,

                -- Determine status
                CASE
                    WHEN COUNT(e.id) = 0 THEN 'Draft'
                    WHEN COUNT(CASE WHEN e.err = -1 THEN 1 END) > 0 THEN
                        CASE
                            WHEN c.queued_ts < NOW() THEN 'Stopped'
                            ELSE 'Paused'
                        END
                    WHEN COUNT(CASE WHEN e.sent = 0 AND e.err >= 0 THEN 1 END) > 0 THEN
                        CASE
                            WHEN c.queued_ts > NOW() THEN 'Scheduled'
                            ELSE 'Sending'
                        END
                    WHEN COUNT(CASE WHEN e.sent > 0 THEN 1 END) = COUNT(e.id) THEN 'Sent'
                    ELSE 'Sending'
                END as status,

                -- Send time (use queued_ts)
                c.queued_ts as send_time

            FROM blog_email_camp c
            LEFT JOIN blog_email_temp t ON c.temp_id = t.id
            LEFT JOIN blog_email e ON c.id = e.camp_id
            WHERE 1=1
                -- Only show recent campaigns (last 30 days) or future scheduled
                AND (c.queued_ts >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                     OR c.queued_ts > NOW()
                     OR EXISTS(SELECT 1 FROM blog_email WHERE camp_id = c.id AND sent = 0))
            GROUP BY c.id, c.temp_id, c.subject, c.txt, c.queued_ts,
                     t.name, t.temp_type, t.users, t.repeat_at
            ORDER BY c.queued_ts DESC
            LIMIT 100
        ";

        $campaigns = $this->db->run($query)->fetchAll(\PDO::FETCH_ASSOC);

        // Transform data to match frontend expectations
        $result = [];
        foreach ($campaigns as $campaign) {
            $result[] = [
                'id' => (int)$campaign['id'],
                'type' => $this->mapEmailType($campaign['temp_type'], $campaign['txt']),
                'icon' => $this->getTypeIcon($campaign['temp_type'], $campaign['txt']),
                'campaign' => $campaign['template_name'] ?: 'Campaign #' . $campaign['id'],
                'subject' => $campaign['subject'] ?: 'No Subject',
                'user_group' => $this->formatUserGroup($campaign['user_group']),
                'num_users' => (int)$campaign['num_users'],
                'queued' => (int)$campaign['queued'],
                'sent' => (int)$campaign['sent'],
                'errors' => (int)$campaign['errors'],
                'opened' => (int)$campaign['opened'],
                'clicked' => (int)$campaign['clicked'],
                'unsubscribed' => (int)$campaign['unsubscribed'],
                'status' => $campaign['status'],
                'send_time' => $campaign['send_time'],
                'actions' => $this->getAvailableActions($campaign['status'])
            ];
        }

        return $result;
    }

    /**
     * Map database temp_type to display type
     */
    private function mapEmailType($temp_type, $txt) {
        if (strpos($txt, '/news/') !== false) {
            return 'News (' . $temp_type . ')';
        }

        switch ($temp_type) {
            case 'Instant': return 'News (Instant)';
            case 'Daily': return 'News (Daily)';
            case 'Weekly': return 'News (Weekly)';
            case 'Manual': return 'Manual';
            default: return $temp_type;
        }
    }

    /**
     * Get icon for email type
     */
    private function getTypeIcon($temp_type, $txt) {
        if (strpos($txt, '/news/') !== false) {
            return 'alarm'; // Instant news
        }

        switch ($temp_type) {
            case 'Instant': return 'alarm';
            case 'Daily': return 'clock';
            case 'Weekly': return 'calendar-week';
            case 'Manual': return 'envelope';
            default: return 'envelope';
        }
    }

    /**
     * Format user group for display
     */
    private function formatUserGroup($user_group) {
        if (!$user_group) return 'All Users';

        $formatted = str_replace(['ALL ', '_'], ['', ' '], $user_group);
        return ucwords(strtolower($formatted));
    }

    /**
     * Get available actions based on status
     */
    private function getAvailableActions($status) {
        switch ($status) {
            case 'Draft':
                return ['Edit', 'Delete'];
            case 'Scheduled':
                return ['Edit', 'Pause', 'Delete'];
            case 'Sending':
                return ['Pause'];
            case 'Paused':
                return ['Resume', 'Delete'];
            case 'Stopped':
                return ['Delete'];
            case 'Sent':
                return ['Edit'];
            default:
                return ['Edit'];
        }
    }
}
